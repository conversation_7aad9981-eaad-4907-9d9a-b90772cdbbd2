#!/usr/bin/env python3
"""
Inspect FMU variables and their properties
"""

import sys
from fmpy import read_model_description

def inspect_fmu_variables(fmu_path):
    """Inspect all variables in the FMU"""
    try:
        model_desc = read_model_description(fmu_path)
        
        print(f"FMU: {model_desc.modelName}")
        print(f"FMI Version: {model_desc.fmiVersion}")
        print(f"GUID: {model_desc.guid}")
        print(f"Description: {getattr(model_desc, 'description', 'N/A')}")
        print("=" * 60)
        
        # Group variables by causality
        variables_by_causality = {}
        for var in model_desc.modelVariables:
            causality = var.causality or 'unknown'
            if causality not in variables_by_causality:
                variables_by_causality[causality] = []
            variables_by_causality[causality].append(var)
        
        # Display variables by category
        for causality, vars_list in variables_by_causality.items():
            print(f"\n{causality.upper()} VARIABLES ({len(vars_list)}):")
            print("-" * 40)
            
            for var in vars_list:
                print(f"  {var.name}")
                print(f"    Type: {var.type}")
                print(f"    Start: {getattr(var, 'start', 'N/A')}")
                print(f"    Description: {getattr(var, 'description', 'N/A')}")
                print(f"    Unit: {getattr(var, 'unit', 'N/A')}")
                
                # Show min/max if available
                if hasattr(var, 'min') and var.min is not None:
                    print(f"    Min: {var.min}")
                if hasattr(var, 'max') and var.max is not None:
                    print(f"    Max: {var.max}")
                
                print()
        
        # Show recommendations for inputs
        input_vars = variables_by_causality.get('input', [])
        if input_vars:
            print("\nRECOMMENDED INPUT INITIALIZATION:")
            print("=" * 40)
            print("start_values = {")
            for var in input_vars:
                if var.start is not None:
                    print(f"    '{var.name}': {var.start},  # {var.type}")
                elif var.type == 'Real':
                    print(f"    '{var.name}': 0.0,  # {var.type} (default)")
                elif var.type == 'Integer':
                    print(f"    '{var.name}': 0,  # {var.type} (default)")
                elif var.type == 'Boolean':
                    print(f"    '{var.name}': False,  # {var.type} (default)")
                else:
                    print(f"    '{var.name}': None,  # {var.type} (unknown default)")
            print("}")
        
        # Show parameters that might need setting
        param_vars = variables_by_causality.get('parameter', [])
        if param_vars:
            print("\nPARAMETERS THAT MIGHT NEED SETTING:")
            print("=" * 40)
            for var in param_vars:
                if var.start is not None:
                    print(f"  {var.name} = {var.start} ({var.type})")
                else:
                    print(f"  {var.name} = ??? ({var.type}) - NO DEFAULT VALUE!")
        
        return True
        
    except Exception as e:
        print(f"Error reading FMU: {e}")
        return False

def generate_test_script(fmu_path):
    """Generate a test script for the FMU"""
    try:
        model_desc = read_model_description(fmu_path)
        
        script_content = f'''#!/usr/bin/env python3
"""
Generated test script for {model_desc.modelName}
"""

from fmpy import simulate_fmu
import os

def test_fmu():
    fmu_path = r"{fmu_path}"
    
    # Add DLL directory to PATH if needed
    dll_dir = r"."  # Assuming DLLs are in current directory
    original_path = os.environ.get('PATH', '')
    os.environ['PATH'] = dll_dir + os.pathsep + original_path
    
    try:
        # Basic simulation
        print("Testing basic simulation...")
        result = simulate_fmu(
            fmu_path,
            stop_time=0.1,  # Very short test
            step_size=0.01,
            validate=False
        )
        print(f"✓ Basic simulation successful! Result shape: {{result.shape}}")
        
        # Test with input initialization
        print("\\nTesting with input initialization...")
        start_values = {{
'''
        
        # Add input variables
        input_vars = [v for v in model_desc.modelVariables if v.causality == 'input']
        for var in input_vars:
            if var.start is not None:
                script_content += f'            "{var.name}": {var.start},\n'
            elif var.type == 'Real':
                script_content += f'            "{var.name}": 0.0,\n'
            elif var.type == 'Integer':
                script_content += f'            "{var.name}": 0,\n'
            elif var.type == 'Boolean':
                script_content += f'            "{var.name}": False,\n'
        
        script_content += '''        }
        
        result = simulate_fmu(
            fmu_path,
            start_values=start_values,
            stop_time=1.0,
            step_size=0.1,
            validate=False
        )
        print(f"✓ Simulation with inputs successful! Result shape: {result.shape}")
        
    except Exception as e:
        print(f"✗ Simulation failed: {e}")
    finally:
        # Restore PATH
        os.environ['PATH'] = original_path

if __name__ == "__main__":
    test_fmu()
'''
        
        with open("test_fmu.py", "w") as f:
            f.write(script_content)
        
        print(f"\n✓ Generated test script: test_fmu.py")
        return True
        
    except Exception as e:
        print(f"Error generating test script: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python inspect_fmu.py <path_to_fmu_file>")
        fmu_path = input("Enter FMU file path: ").strip().strip('"')
    else:
        fmu_path = sys.argv[1]
    
    if not fmu_path.endswith('.fmu'):
        print("Error: Please provide a .fmu file")
        return
    
    print("FMU Variable Inspector")
    print("=" * 60)
    
    if inspect_fmu_variables(fmu_path):
        print("\n" + "=" * 60)
        generate_test_script(fmu_path)

if __name__ == "__main__":
    main()
