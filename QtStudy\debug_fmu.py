#!/usr/bin/env python3
"""
Debug FMU loading step by step to isolate the crash
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def setup_dll_path():
    """Add current directory to PATH for DLLs"""
    current_dir = os.getcwd()
    original_path = os.environ.get('PATH', '')
    os.environ['PATH'] = current_dir + os.pathsep + original_path
    print(f"✓ Added {current_dir} to PATH")
    return original_path

def test_fmu_loading(fmu_path):
    """Test FMU loading step by step"""
    print(f"Testing FMU: {fmu_path}")
    print("=" * 50)
    
    # Step 1: Check file exists
    try:
        if not os.path.exists(fmu_path):
            print(f"✗ FMU file not found: {fmu_path}")
            return False
        print(f"✓ FMU file exists ({os.path.getsize(fmu_path):,} bytes)")
    except Exception as e:
        print(f"✗ Error checking file: {e}")
        return False
    
    # Step 2: Try to read model description
    try:
        from fmpy import read_model_description
        print("✓ FMPy imported successfully")
        
        model_desc = read_model_description(fmu_path)
        print(f"✓ Model description read successfully")
        print(f"  Model: {model_desc.modelName}")
        print(f"  FMI Version: {model_desc.fmiVersion}")
        print(f"  GUID: {model_desc.guid}")
        
        # Count variables
        inputs = [v for v in model_desc.modelVariables if v.causality == 'input']
        outputs = [v for v in model_desc.modelVariables if v.causality == 'output']
        params = [v for v in model_desc.modelVariables if v.causality == 'parameter']
        
        print(f"  Variables: {len(inputs)} inputs, {len(outputs)} outputs, {len(params)} parameters")
        
    except Exception as e:
        print(f"✗ Error reading model description: {e}")
        return False
    
    # Step 3: Try to extract FMU
    try:
        from fmpy import extract
        temp_dir = tempfile.mkdtemp()
        extract_dir = extract(fmu_path, temp_dir)
        print(f"✓ FMU extracted to: {extract_dir}")
        
        # Check for DLL files
        dll_files = []
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.endswith('.dll'):
                    dll_path = os.path.join(root, file)
                    dll_files.append(dll_path)
        
        print(f"✓ Found {len(dll_files)} DLL files:")
        for dll in dll_files:
            print(f"  - {os.path.basename(dll)}")
        
    except Exception as e:
        print(f"✗ Error extracting FMU: {e}")
        return False
    
    # Step 4: Try to instantiate FMU (this is where crashes often occur)
    try:
        from fmpy.fmi2 import FMU2Slave
        print("Attempting to instantiate FMU...")
        
        # This is the critical step that often causes access violations
        fmu = FMU2Slave(
            guid=model_desc.guid,
            unzipDirectory=extract_dir,
            modelIdentifier=model_desc.coSimulation.modelIdentifier,
            instanceName="test_instance"
        )
        print("✓ FMU instantiated successfully!")
        
        # Try to initialize
        print("Attempting to initialize FMU...")
        fmu.instantiate()
        print("✓ FMU instantiate() called successfully!")
        
        # Try setup experiment
        fmu.setupExperiment(startTime=0.0)
        print("✓ FMU setupExperiment() called successfully!")
        
        # Try enter initialization mode
        fmu.enterInitializationMode()
        print("✓ FMU enterInitializationMode() called successfully!")
        
        # Try exit initialization mode
        fmu.exitInitializationMode()
        print("✓ FMU exitInitializationMode() called successfully!")
        
        # Try a single step
        fmu.doStep(currentCommunicationPoint=0.0, communicationStepSize=0.1)
        print("✓ FMU doStep() called successfully!")
        
        # Clean up
        fmu.terminate()
        fmu.freeInstance()
        print("✓ FMU terminated and freed successfully!")
        
    except Exception as e:
        print(f"✗ Error during FMU instantiation/initialization: {e}")
        print(f"   This is likely where the access violation occurs!")
        return False
    finally:
        # Clean up temp directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    # Step 5: Try high-level simulation (if we got this far)
    try:
        from fmpy import simulate_fmu
        print("Attempting high-level simulation...")
        
        result = simulate_fmu(
            fmu_path,
            stop_time=0.01,  # Very short
            step_size=0.001,
            validate=False,
            debug_logging=False
        )
        print(f"✓ High-level simulation successful! Result shape: {result.shape}")
        
    except Exception as e:
        print(f"✗ High-level simulation failed: {e}")
        return False
    
    return True

def suggest_fixes():
    """Suggest potential fixes based on common issues"""
    print("\n" + "=" * 50)
    print("POTENTIAL FIXES FOR ACCESS VIOLATIONS:")
    print("=" * 50)
    
    print("\n1. FMU COMPILATION ISSUES:")
    print("   - FMU may have been compiled with debug symbols")
    print("   - Try a release build of the FMU")
    print("   - Contact FMU developer for a different build")
    
    print("\n2. MEMORY ALIGNMENT ISSUES:")
    print("   - Some FMUs have memory alignment problems")
    print("   - Try running on a different machine")
    print("   - Try with 64-bit Python if available")
    
    print("\n3. INITIALIZATION SEQUENCE:")
    print("   - FMU may require specific initialization order")
    print("   - Some variables may need to be set before initialization")
    print("   - Check FMU documentation for initialization requirements")
    
    print("\n4. DEPENDENCY ISSUES:")
    print("   - Even though DLLs load, they may have runtime dependencies")
    print("   - Try copying ALL DLLs from original FMU development environment")
    print("   - Check if FMU needs specific runtime libraries")
    
    print("\n5. COMPATIBILITY ISSUES:")
    print("   - FMU may not be compatible with FMPy")
    print("   - Try different FMU simulation tools (e.g., OpenModelica, Dymola)")
    print("   - Try older version of FMPy")

def main():
    print("FMU Debug Tool - Step-by-Step Analysis")
    print("=" * 50)
    
    # Setup DLL path
    original_path = setup_dll_path()
    
    try:
        # Get FMU path
        if len(sys.argv) > 1:
            fmu_path = sys.argv[1]
        else:
            fmu_path = input("Enter path to FMU file: ").strip().strip('"')
        
        if not fmu_path.endswith('.fmu'):
            print("Error: Please provide a .fmu file")
            return
        
        # Run the test
        success = test_fmu_loading(fmu_path)
        
        if not success:
            suggest_fixes()
        else:
            print("\n✓ All tests passed! The FMU should work normally.")
    
    finally:
        # Restore PATH
        os.environ['PATH'] = original_path

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
