#!/usr/bin/env python3
"""
Check what DLLs you have and what's missing
"""

import os
import ctypes

def check_dll_in_current_dir():
    """Check DLLs in current directory"""
    print("Checking DLLs in current directory...")
    print("-" * 40)
    
    dll_files = [f for f in os.listdir('.') if f.endswith('.dll')]
    
    if dll_files:
        print("Found DLL files:")
        for dll in dll_files:
            size = os.path.getsize(dll)
            print(f"  ✓ {dll} ({size:,} bytes)")
    else:
        print("  No DLL files found in current directory")
    
    return dll_files

def check_missing_dlls():
    """Check for missing DLLs that are commonly needed"""
    print("\nChecking for missing MinGW runtime DLLs...")
    print("-" * 40)
    
    mingw_dlls = [
        "libgcc_s_dw2-1.dll",
        "libstdc++-6.dll", 
        "libwinpthread-1.dll",
        "libgomp-1.dll"
    ]
    
    missing = []
    found = []
    
    for dll in mingw_dlls:
        try:
            # Try to load the DLL
            ctypes.WinDLL(dll)
            found.append(dll)
            print(f"  ✓ {dll} - Available")
        except OSError:
            missing.append(dll)
            print(f"  ✗ {dll} - MISSING")
    
    return missing, found

def suggest_solutions(missing_dlls):
    """Suggest solutions for missing DLLs"""
    if not missing_dlls:
        print("\n✓ All common MinGW DLLs are available!")
        return
    
    print(f"\n⚠ Missing DLLs: {', '.join(missing_dlls)}")
    print("\nSOLUTIONS:")
    print("="*50)
    
    print("\n1. DOWNLOAD MISSING DLLs:")
    for dll in missing_dlls:
        print(f"   - {dll}: Search on dll-files.com or similar")
    
    print("\n2. INSTALL MSYS2 (Recommended):")
    print("   - Download from: https://www.msys2.org/")
    print("   - Install and add C:\\msys64\\mingw32\\bin to PATH")
    
    print("\n3. COPY FROM EXISTING MINGW:")
    print("   - If you have MinGW/Code::Blocks/Dev-C++ installed")
    print("   - Look in their bin/ directories")
    
    print("\n4. PLACE DLLs IN:")
    print("   - Same folder as your Python script")
    print("   - C:\\Windows\\SysWOW64\\ (for 32-bit DLLs)")
    print("   - Any folder in your PATH")

def main():
    print("DLL Checker for FMU Simulation")
    print("="*40)
    
    # Check current directory
    local_dlls = check_dll_in_current_dir()
    
    # Check system for MinGW DLLs
    missing, found = check_missing_dlls()
    
    # Provide solutions
    suggest_solutions(missing)
    
    print(f"\nSUMMARY:")
    print(f"  Local DLLs: {len(local_dlls)}")
    print(f"  System MinGW DLLs found: {len(found)}")
    print(f"  Missing MinGW DLLs: {len(missing)}")
    
    if missing:
        print(f"\n🔧 QUICK FIX: Copy these DLLs to current directory:")
        for dll in missing:
            print(f"     {dll}")

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
