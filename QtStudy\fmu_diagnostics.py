#!/usr/bin/env python3
"""
FMU Diagnostics Tool
This script helps diagnose common FMU loading issues on Windows
"""

import os
import sys
import subprocess
import tempfile
import shutil
from fmpy import extract, read_model_description

def check_python_architecture():
    """Check if Python is 32-bit or 64-bit"""
    import platform
    arch = platform.architecture()[0]
    print(f"Python architecture: {arch}")
    return arch

def check_vcredist():
    """Check for Visual C++ Redistributables"""
    print("\nChecking for Visual C++ Redistributables...")
    try:
        # Check registry for installed redistributables
        import winreg
        
        # Common registry paths for VC++ redistributables
        paths = [
            r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64",
            r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86",
            r"SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x64",
            r"SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x86",
        ]
        
        found_any = False
        for path in paths:
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path)
                version = winreg.QueryValueEx(key, "Version")[0]
                print(f"  Found VC++ Redistributable: {path} - Version: {version}")
                found_any = True
                winreg.CloseKey(key)
            except FileNotFoundError:
                continue
        
        if not found_any:
            print("  No Visual C++ Redistributables found in registry")
            print("  Consider installing Microsoft Visual C++ Redistributable")
            
    except ImportError:
        print("  Cannot check registry (winreg not available)")

def analyze_fmu(fmu_path):
    """Analyze FMU structure and dependencies"""
    print(f"\nAnalyzing FMU: {fmu_path}")
    
    if not os.path.exists(fmu_path):
        print(f"ERROR: FMU file not found: {fmu_path}")
        return
    
    try:
        # Read model description
        model_desc = read_model_description(fmu_path)
        print(f"FMU Name: {model_desc.modelName}")
        print(f"FMI Version: {model_desc.fmiVersion}")
        print(f"GUID: {model_desc.guid}")
        
        # Extract FMU
        temp_dir = tempfile.mkdtemp()
        extract_dir = extract(fmu_path, temp_dir)
        print(f"Extracted to: {extract_dir}")
        
        # Find DLL files
        dll_files = []
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.endswith('.dll'):
                    dll_path = os.path.join(root, file)
                    dll_files.append(dll_path)
                    print(f"Found DLL: {dll_path}")
        
        # Analyze each DLL
        for dll in dll_files:
            analyze_dll(dll)
        
        # Clean up
        shutil.rmtree(temp_dir)
        
    except Exception as e:
        print(f"ERROR analyzing FMU: {e}")

def analyze_dll(dll_path):
    """Analyze DLL dependencies"""
    print(f"\nAnalyzing DLL: {os.path.basename(dll_path)}")
    
    # Check if file exists and is readable
    if not os.path.exists(dll_path):
        print(f"  ERROR: DLL not found")
        return
    
    # Try to get file info
    try:
        stat = os.stat(dll_path)
        print(f"  Size: {stat.st_size} bytes")
    except Exception as e:
        print(f"  Cannot get file stats: {e}")
    
    # Try to analyze with dumpbin (Visual Studio tool)
    try:
        result = subprocess.run(['dumpbin', '/dependents', dll_path], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("  Dependencies (dumpbin):")
            lines = result.stdout.split('\n')
            in_deps = False
            for line in lines:
                line = line.strip()
                if 'Image has the following dependencies:' in line:
                    in_deps = True
                    continue
                elif in_deps and line and not line.startswith('Summary'):
                    if line.endswith('.dll'):
                        print(f"    - {line}")
                elif 'Summary' in line:
                    break
        else:
            print(f"  dumpbin failed: {result.stderr}")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("  dumpbin not available")
        
        # Try objdump as fallback
        try:
            result = subprocess.run(['objdump', '-p', dll_path], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("  Dependencies (objdump):")
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'DLL Name:' in line:
                        dll_name = line.split('DLL Name:')[1].strip()
                        print(f"    - {dll_name}")
            else:
                print(f"  objdump failed: {result.stderr}")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("  No dependency analysis tools available")

def suggest_solutions():
    """Suggest common solutions for FMU loading issues"""
    print("\n" + "="*60)
    print("COMMON SOLUTIONS FOR FMU LOADING ISSUES:")
    print("="*60)
    
    print("\n1. INSTALL VISUAL C++ REDISTRIBUTABLES:")
    print("   Download from Microsoft:")
    print("   - VC++ 2015-2022 Redistributable (x64)")
    print("   - VC++ 2015-2022 Redistributable (x86)")
    print("   - Older versions if needed (2013, 2012, 2010)")
    
    print("\n2. CHECK PYTHON ARCHITECTURE:")
    print("   - Use 64-bit Python for 64-bit FMUs")
    print("   - Use 32-bit Python for 32-bit FMUs")
    
    print("\n3. COPY DEPENDENCIES:")
    print("   - Copy missing DLLs to FMU's binaries folder")
    print("   - Or add DLL locations to PATH environment variable")
    
    print("\n4. TRY DIFFERENT FMI VERSIONS:")
    print("   - Some FMUs work better with specific FMI versions")
    print("   - Try FMI 1.0 vs FMI 2.0 versions if available")
    
    print("\n5. USE DEPENDENCY WALKER:")
    print("   - Download Dependency Walker (depends.exe)")
    print("   - Analyze the FMU's DLL to see missing dependencies")

def main():
    print("FMU Diagnostics Tool")
    print("="*40)
    
    # Check Python architecture
    check_python_architecture()
    
    # Check for Visual C++ Redistributables
    check_vcredist()
    
    # Get FMU path from command line or user input
    if len(sys.argv) > 1:
        fmu_path = sys.argv[1]
    else:
        fmu_path = input("\nEnter path to FMU file (or press Enter to skip): ").strip()
    
    if fmu_path and fmu_path.lower().endswith('.fmu'):
        analyze_fmu(fmu_path)
    
    # Show suggestions
    suggest_solutions()

if __name__ == "__main__":
    main()
