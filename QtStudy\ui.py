from PyQt5 import QtCore, QtWidgets
from fmpy import simulate_fmu, read_model_description, extract
import sys
import os
import tempfile
import shutil
import subprocess

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1000, 1000)
        self.centralwidget = QtWidgets.QWidget(parent=MainWindow)
        self.centralwidget.setObjectName("centralwidget")

        # Button: Select FMU
        self.btnSelectFMU = QtWidgets.QPushButton(parent=self.centralwidget)
        self.btnSelectFMU.setGeometry(QtCore.QRect(40, 30, 170, 40))
        self.btnSelectFMU.setText("Select FMU")

        # Label: FMU Path
        self.labelFMUPath = QtWidgets.QLabel(parent=self.centralwidget)
        self.labelFMUPath.setGeometry(QtCore.QRect(230, 30, 420, 40))
        self.labelFMUPath.setText("No FMU selected")

        # Input variables box
        self.inputBox = QtWidgets.QTextEdit(parent=self.centralwidget)
        self.inputBox.setGeometry(QtCore.QRect(40, 90, 280, 140))
        self.inputBox.setReadOnly(True)
        self.inputBox.setPlaceholderText("Input variables will be listed here...")

        # Output variables box
        self.outputBox = QtWidgets.QTextEdit(parent=self.centralwidget)
        self.outputBox.setGeometry(QtCore.QRect(370, 90, 280, 140))
        self.outputBox.setReadOnly(True)
        self.outputBox.setPlaceholderText("Output variables will be listed here...")

        # Button: Run
        self.btnRun = QtWidgets.QPushButton(parent=self.centralwidget)
        self.btnRun.setGeometry(QtCore.QRect(40, 250, 170, 40))
        self.btnRun.setText("Run FMU")

        # Status/log box
        self.textLog = QtWidgets.QTextEdit(parent=self.centralwidget)
        self.textLog.setGeometry(QtCore.QRect(70, 310, 610, 50))
        self.textLog.setReadOnly(True)
        self.textLog.setPlaceholderText("Status and messages...")

        # Output result box
        self.textOutput = QtWidgets.QTextEdit(parent=self.centralwidget)
        self.textOutput.setGeometry(QtCore.QRect(40, 370, 610, 100))
        self.textOutput.setReadOnly(True)
        self.textOutput.setPlaceholderText("FMU outputs will appear here...")

        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(parent=MainWindow)
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(parent=MainWindow)
        MainWindow.setStatusBar(self.statusbar)

        # Connect events
        self.btnSelectFMU.clicked.connect(self.select_fmu)
        self.btnRun.clicked.connect(self.run_fmu)
        self.selected_fmu_path = None

    def check_dll_dependencies(self, dll_path):
        """Check DLL dependencies using dumpbin or similar tools"""
        try:
            # Try to use dumpbin if available (part of Visual Studio)
            result = subprocess.run(['dumpbin', '/dependents', dll_path],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout
            else:
                return f"dumpbin failed: {result.stderr}"
        except (subprocess.TimeoutExpired, FileNotFoundError):
            # dumpbin not available, try objdump (if mingw is installed)
            try:
                result = subprocess.run(['objdump', '-p', dll_path],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return result.stdout
                else:
                    return f"objdump failed: {result.stderr}"
            except (subprocess.TimeoutExpired, FileNotFoundError):
                return "No dependency analysis tools available (dumpbin/objdump)"

    def validate_fmu_file(self, fmu_path):
        """Validate FMU and extract information about potential issues"""
        try:
            # Extract FMU to temporary directory
            temp_dir = tempfile.mkdtemp()
            extract_dir = extract(fmu_path, temp_dir)

            # Look for DLL files
            dll_files = []
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    if file.endswith('.dll'):
                        dll_files.append(os.path.join(root, file))

            info = {
                'extract_dir': extract_dir,
                'dll_files': dll_files,
                'temp_dir': temp_dir
            }

            # Check dependencies for each DLL
            for dll in dll_files:
                deps = self.check_dll_dependencies(dll)
                info[f'deps_{os.path.basename(dll)}'] = deps

            return info

        except Exception as e:
            return {'error': str(e)}

    def cleanup_temp_dir(self, temp_dir):
        """Clean up temporary directory"""
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"Failed to cleanup temp dir: {e}")

    def select_fmu(self):
        file_dialog = QtWidgets.QFileDialog()
        fmu_path, _ = file_dialog.getOpenFileName(None, "Select FMU File", "", "FMU Files (*.fmu)")
        if fmu_path:
            self.selected_fmu_path = fmu_path
            self.labelFMUPath.setText(os.path.basename(fmu_path))
            self.textLog.setText(f"Selected: {fmu_path}")
            self.textOutput.clear()

            # Read model description and list variables
            try:
                model_description = read_model_description(fmu_path)
                input_variables = [v.name for v in model_description.modelVariables if v.causality == 'input']
                output_variables = [v.name for v in model_description.modelVariables if v.causality == 'output']
                self.inputBox.setText('\n'.join(input_variables) if input_variables else "No input variables found.")
                self.outputBox.setText('\n'.join(output_variables) if output_variables else "No output variables found.")
            except Exception as e:
                self.inputBox.setText("Error reading FMU")
                self.outputBox.setText("Error reading FMU")
                self.textLog.append(f"Failed to read FMU: {e}")

    def run_fmu(self):
        if not self.selected_fmu_path:
            self.textLog.setText("Please select an FMU file first!")
            return

        temp_dir = None
        try:
            self.textLog.setText("Running FMU simulation...")

            # First, validate the FMU and check for potential issues
            self.textLog.append("Validating FMU...")
            fmu_info = self.validate_fmu_file(self.selected_fmu_path)

            if 'error' in fmu_info:
                self.textLog.append(f"FMU validation failed: {fmu_info['error']}")
                return

            temp_dir = fmu_info.get('temp_dir')
            dll_files = fmu_info.get('dll_files', [])

            self.textLog.append(f"Found {len(dll_files)} DLL files in FMU")
            for dll in dll_files:
                self.textLog.append(f"  - {os.path.basename(dll)}")

            # Read model description
            model_description = read_model_description(self.selected_fmu_path)
            output_variables = [v.name for v in model_description.modelVariables if v.causality == 'output']
            self.textLog.append(f"Output variables: {output_variables}")

            # Try to simulate
            self.textLog.append("Starting simulation...")
            result = simulate_fmu(self.selected_fmu_path, output=['*'], stop_time=2.0)
            self.textLog.append(f"Result dtype.names: {result.dtype.names}")
            self.textLog.append(f"Result shape: {result.shape}")

            n_show = 5
            lines = ["FMU OUTPUT VARIABLES:"]
            if len(result) == 0:
                lines.append("Result array is empty! No simulation data.")
            else:
                for name in output_variables:
                    if name in result.dtype.names:
                        values = result[name][:n_show]
                        lines.append(f"{name}: {values}")
                    else:
                        lines.append(f"{name}: (Not in result)")
            self.textOutput.setText('\n'.join(lines))
            self.textLog.append("Simulation finished successfully.")

        except Exception as e:
            error_msg = str(e)
            self.textOutput.setText("")

            # Provide specific guidance for DLL loading errors
            if "Failed to load shared library" in error_msg or "Could not find module" in error_msg:
                self.textLog.setText("DLL LOADING ERROR DETECTED!\n")
                self.textLog.append("This error typically occurs due to:")
                self.textLog.append("1. Missing Visual C++ Redistributables")
                self.textLog.append("2. Missing dependencies (other DLLs)")
                self.textLog.append("3. Architecture mismatch (32-bit vs 64-bit)")
                self.textLog.append("4. Missing runtime libraries\n")

                self.textLog.append("SOLUTIONS TO TRY:")
                self.textLog.append("1. Install Microsoft Visual C++ Redistributable")
                self.textLog.append("2. Use 64-bit Python if FMU is 64-bit")
                self.textLog.append("3. Check FMU documentation for dependencies")
                self.textLog.append("4. Try running from FMU's original directory\n")

                # Show dependency information if available
                if temp_dir and 'dll_files' in locals():
                    for dll in dll_files:
                        dll_name = os.path.basename(dll)
                        deps_key = f'deps_{dll_name}'
                        if deps_key in fmu_info:
                            self.textLog.append(f"Dependencies for {dll_name}:")
                            self.textLog.append(fmu_info[deps_key][:500] + "...")  # Truncate long output

                self.textLog.append(f"\nOriginal error: {error_msg}")
            else:
                self.textLog.setText(f"Error while running FMU:\n{error_msg}")

        finally:
            # Clean up temporary directory
            if temp_dir:
                self.cleanup_temp_dir(temp_dir)

    

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.setWindowTitle("FMU Runner with FMPY")
    MainWindow.show()
    sys.exit(app.exec())
