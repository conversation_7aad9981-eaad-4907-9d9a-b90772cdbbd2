#!/usr/bin/env python3
"""
UI kế thừa từ FMPy để sử dụng chính xác cùng initialization logic
"""

import os
import sys
from PyQt5 import QtCore, QtWidgets
from fmpy import read_model_description

class FMPyInheritedSimulator:
    """Class kế thừa logic simulation từ FMPy"""
    
    def __init__(self):
        self.setup_environment()
    
    def setup_environment(self):
        """Setup environment giống FMPy GUI"""
        # Add current directory to PATH for DLLs
        current_dir = os.getcwd()
        original_path = os.environ.get('PATH', '')
        os.environ['PATH'] = current_dir + os.pathsep + original_path
        
        # Set FMPy environment variables
        os.environ['FMPY_GUI_QT_BACKEND'] = 'PySide2'
    
    def simulate_with_fmpy_logic(self, fmu_path):
        """Sử dụng chính xác logic của FMPy để simulate"""
        try:
            # Import FMPy simulation logic
            from fmpy.simulation import simulate_fmu as fmpy_simulate
            from fmpy.util import read_model_description, validate_fmu
            
            print("Using FMPy's internal simulation logic...")
            
            # Đọc model description
            model_description = read_model_description(fmu_path)
            print(f"Model: {model_description.modelName}")
            
            # Validate FMU (như FMPy GUI làm)
            problems = validate_fmu(fmu_path)
            if problems:
                print(f"FMU validation warnings: {len(problems)} issues found")
                for problem in problems[:3]:  # Show first 3
                    print(f"  - {problem}")
            
            # Sử dụng FMPy's default simulation parameters
            result = fmpy_simulate(
                filename=fmu_path,
                validate=True,
                start_time=0.0,
                stop_time=2.0,
                solver='CVode',
                step_size=None,
                output_interval=None,
                record_inputs=True,
                record_outputs=True,
                apply_default_start_values=True,
                debug_logging=False,
                visible=False
            )
            
            return result
            
        except Exception as e:
            print(f"FMPy logic simulation failed: {e}")
            return None
    
    def simulate_with_gui_class(self, fmu_path):
        """Thử sử dụng trực tiếp class từ FMPy GUI"""
        try:
            # Import FMPy GUI classes
            from fmpy.gui.MainWindow import MainWindow as FMPyMainWindow
            
            print("Attempting to use FMPy GUI class directly...")
            
            # Tạo instance của FMPy GUI MainWindow (không hiển thị)
            # và sử dụng simulation method của nó
            
            # Đây là approach experimental - có thể cần adjust
            return None
            
        except Exception as e:
            print(f"GUI class approach failed: {e}")
            return None
    
    def simulate_with_low_level_fmi(self, fmu_path):
        """Sử dụng low-level FMI interface như FMPy GUI"""
        try:
            from fmpy import extract, read_model_description
            from fmpy.fmi2 import FMU2Slave
            import tempfile
            import shutil
            
            print("Using low-level FMI interface...")
            
            # Đọc model description
            model_desc = read_model_description(fmu_path)
            
            # Extract FMU
            temp_dir = tempfile.mkdtemp()
            extract_dir = extract(fmu_path, temp_dir)
            
            try:
                # Tạo FMU instance với settings giống GUI
                fmu = FMU2Slave(
                    guid=model_desc.guid,
                    unzipDirectory=extract_dir,
                    modelIdentifier=model_desc.coSimulation.modelIdentifier,
                    instanceName="gui_like_instance"
                )
                
                # Instantiate
                fmu.instantiate()
                print("✓ FMU instantiated")
                
                # Setup experiment với default values
                fmu.setupExperiment(
                    toleranceDefined=False,
                    tolerance=0.0,
                    startTime=0.0,
                    stopTimeDefined=True,
                    stopTime=2.0
                )
                print("✓ Experiment setup")
                
                # Enter initialization mode
                fmu.enterInitializationMode()
                print("✓ Entered initialization mode")
                
                # Set default start values (quan trọng!)
                input_vars = [v for v in model_desc.modelVariables if v.causality == 'input']
                for var in input_vars:
                    if var.start is not None:
                        if var.type == 'Real':
                            fmu.setReal([var.valueReference], [float(var.start)])
                        elif var.type == 'Integer':
                            fmu.setInteger([var.valueReference], [int(var.start)])
                        elif var.type == 'Boolean':
                            fmu.setBoolean([var.valueReference], [bool(var.start)])
                        print(f"Set {var.name} = {var.start}")
                
                # Exit initialization mode
                fmu.exitInitializationMode()
                print("✓ Exited initialization mode")
                
                # Simulate một vài steps
                time = 0.0
                step_size = 0.1
                results = []
                
                for i in range(10):  # 10 steps
                    fmu.doStep(currentCommunicationPoint=time, communicationStepSize=step_size)
                    time += step_size
                    
                    # Get outputs
                    output_vars = [v for v in model_desc.modelVariables if v.causality == 'output']
                    if output_vars:
                        output_refs = [v.valueReference for v in output_vars[:5]]  # First 5 outputs
                        values = fmu.getReal(output_refs)
                        results.append((time, values))
                        print(f"t={time:.1f}: {values}")
                
                # Cleanup
                fmu.terminate()
                fmu.freeInstance()
                
                print("✓ Low-level simulation completed successfully!")
                return results
                
            finally:
                shutil.rmtree(temp_dir)
                
        except Exception as e:
            print(f"Low-level FMI simulation failed: {e}")
            import traceback
            traceback.print_exc()
            return None

class FMPyInheritedUI(QtWidgets.QMainWindow):
    """UI kế thừa logic từ FMPy"""
    
    def __init__(self):
        super().__init__()
        self.simulator = FMPyInheritedSimulator()
        self.selected_fmu_path = None
        self.setupUi()
    
    def setupUi(self):
        """Setup UI đơn giản"""
        self.setWindowTitle("FMPy Inherited Simulator")
        self.resize(800, 600)
        
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # FMU selection
        fmu_layout = QtWidgets.QHBoxLayout()
        self.btn_select = QtWidgets.QPushButton("Select FMU")
        self.label_fmu = QtWidgets.QLabel("No FMU selected")
        fmu_layout.addWidget(self.btn_select)
        fmu_layout.addWidget(self.label_fmu)
        layout.addLayout(fmu_layout)
        
        # Simulation buttons
        button_layout = QtWidgets.QHBoxLayout()
        self.btn_fmpy_logic = QtWidgets.QPushButton("Simulate with FMPy Logic")
        self.btn_low_level = QtWidgets.QPushButton("Simulate with Low-level FMI")
        button_layout.addWidget(self.btn_fmpy_logic)
        button_layout.addWidget(self.btn_low_level)
        layout.addLayout(button_layout)
        
        # Results
        self.text_results = QtWidgets.QTextEdit()
        self.text_results.setReadOnly(True)
        layout.addWidget(self.text_results)
        
        # Connect signals
        self.btn_select.clicked.connect(self.select_fmu)
        self.btn_fmpy_logic.clicked.connect(self.simulate_fmpy_logic)
        self.btn_low_level.clicked.connect(self.simulate_low_level)
    
    def select_fmu(self):
        """Select FMU file"""
        file_dialog = QtWidgets.QFileDialog()
        fmu_path, _ = file_dialog.getOpenFileName(self, "Select FMU File", "", "FMU Files (*.fmu)")
        if fmu_path:
            self.selected_fmu_path = fmu_path
            self.label_fmu.setText(os.path.basename(fmu_path))
            self.text_results.append(f"Selected: {fmu_path}")
    
    def simulate_fmpy_logic(self):
        """Simulate using FMPy's internal logic"""
        if not self.selected_fmu_path:
            self.text_results.append("Please select an FMU first!")
            return
        
        self.text_results.append("\n=== FMPy Logic Simulation ===")
        try:
            result = self.simulator.simulate_with_fmpy_logic(self.selected_fmu_path)
            if result is not None:
                self.text_results.append(f"✓ SUCCESS! Result shape: {result.shape}")
                self.text_results.append(f"Columns: {list(result.dtype.names)}")
            else:
                self.text_results.append("✗ Simulation failed")
        except Exception as e:
            self.text_results.append(f"✗ Error: {e}")
    
    def simulate_low_level(self):
        """Simulate using low-level FMI"""
        if not self.selected_fmu_path:
            self.text_results.append("Please select an FMU first!")
            return
        
        self.text_results.append("\n=== Low-level FMI Simulation ===")
        try:
            result = self.simulator.simulate_with_low_level_fmi(self.selected_fmu_path)
            if result is not None:
                self.text_results.append(f"✓ SUCCESS! Got {len(result)} time points")
            else:
                self.text_results.append("✗ Simulation failed")
        except Exception as e:
            self.text_results.append(f"✗ Error: {e}")

def main():
    app = QtWidgets.QApplication(sys.argv)
    window = FMPyInheritedUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
