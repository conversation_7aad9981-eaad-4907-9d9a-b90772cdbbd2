#!/usr/bin/env python3
"""
Fix MinGW DLL dependency issue for FMU
"""

import os
import sys
import urllib.request
import shutil
from pathlib import Path

def download_mingw_dll():
    """Download libgcc_s_dw2-1.dll"""
    dll_name = "libgcc_s_dw2-1.dll"
    
    # Common locations where this DLL might be found
    possible_sources = [
        "https://github.com/nalexandru/api-ms-win-core-path-HACK/raw/master/dll/x86/libgcc_s_dw2-1.dll",
        # Add more sources if needed
    ]
    
    # Try to find the DLL in system directories first
    system_paths = [
        r"C:\MinGW\bin",
        r"C:\mingw32\bin", 
        r"C:\msys64\mingw32\bin",
        r"C:\Program Files\mingw-w64\mingw32\bin",
        r"C:\Program Files (x86)\mingw-w64\mingw32\bin",
    ]
    
    print(f"Looking for {dll_name}...")
    
    # Check if DLL already exists in system
    for path in system_paths:
        dll_path = os.path.join(path, dll_name)
        if os.path.exists(dll_path):
            print(f"Found {dll_name} at: {dll_path}")
            return dll_path
    
    # Check in current directory
    if os.path.exists(dll_name):
        print(f"Found {dll_name} in current directory")
        return os.path.abspath(dll_name)
    
    print(f"{dll_name} not found in system. You need to:")
    print("1. Install MinGW-w64 or MSYS2")
    print("2. Or download the DLL manually")
    print("3. Or copy it from another MinGW installation")
    
    return None

def copy_dll_to_system():
    """Copy DLL to Windows system directory"""
    dll_name = "libgcc_s_dw2-1.dll"
    dll_path = download_mingw_dll()
    
    if not dll_path:
        return False
    
    # Copy to System32 (for 32-bit DLLs on 64-bit Windows, use SysWOW64)
    import platform
    if platform.machine().endswith('64'):
        system_dir = r"C:\Windows\SysWOW64"  # For 32-bit DLLs on 64-bit Windows
    else:
        system_dir = r"C:\Windows\System32"
    
    dest_path = os.path.join(system_dir, dll_name)
    
    try:
        shutil.copy2(dll_path, dest_path)
        print(f"Successfully copied {dll_name} to {dest_path}")
        return True
    except PermissionError:
        print(f"Permission denied. Run as administrator to copy to {system_dir}")
        return False
    except Exception as e:
        print(f"Error copying DLL: {e}")
        return False

def add_dll_to_path():
    """Add DLL directory to PATH"""
    dll_path = download_mingw_dll()
    if dll_path:
        dll_dir = os.path.dirname(dll_path)
        current_path = os.environ.get('PATH', '')
        if dll_dir not in current_path:
            os.environ['PATH'] = dll_dir + os.pathsep + current_path
            print(f"Added {dll_dir} to PATH for this session")
            return True
    return False

def create_local_solution():
    """Create a local solution by copying DLL to current directory"""
    dll_name = "libgcc_s_dw2-1.dll"
    
    print("MANUAL SOLUTION:")
    print("="*50)
    print(f"1. Download {dll_name} from a reliable source:")
    print("   - Install MSYS2: https://www.msys2.org/")
    print("   - Or download from: https://www.dll-files.com/libgcc_s_dw2-1.dll.html")
    print("   - Or copy from existing MinGW installation")
    print()
    print(f"2. Place {dll_name} in one of these locations:")
    print("   a) Same directory as your Python script")
    print("   b) C:\\Windows\\SysWOW64\\ (requires admin rights)")
    print("   c) Any directory in your PATH environment variable")
    print()
    print("3. Alternative: Install MinGW-w64 or MSYS2:")
    print("   - MSYS2: https://www.msys2.org/")
    print("   - MinGW-w64: https://www.mingw-w64.org/downloads/")

def main():
    print("MinGW DLL Dependency Fixer")
    print("="*40)
    print("Your FMU needs libgcc_s_dw2-1.dll (MinGW runtime)")
    print()
    
    # Try to find and add DLL to PATH
    if add_dll_to_path():
        print("✓ DLL found and added to PATH")
        print("Try running your FMU simulation again.")
    else:
        print("✗ DLL not found in system")
        create_local_solution()
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
