#!/usr/bin/env python3
"""
Try to replicate the exact environment and process that FMPy GUI uses
"""

import os
import sys
import subprocess
import tempfile
import shutil

def run_fmpy_gui_programmatically(fmu_path):
    """Run FMPy GUI programmatically and capture what it does"""
    print("Attempting to run FMPy GUI programmatically...")
    print("=" * 50)
    
    try:
        # Method 1: Try to import and run FMPy GUI directly
        print("Method 1: Direct FMPy GUI import...")
        
        # Set the same environment variable as your Run.bat
        os.environ['FMPY_GUI_QT_BACKEND'] = 'PySide2'
        
        # Import FMPy GUI
        from fmpy.gui import main as fmpy_gui_main
        
        # This won't work directly because GUI needs interaction,
        # but let's see if we can access the simulation code
        print("✓ FMPy GUI imported successfully")
        
    except Exception as e:
        print(f"✗ Method 1 failed: {e}")
    
    try:
        # Method 2: Try to access FMPy GUI's simulation function directly
        print("\nMethod 2: Access FMPy GUI simulation function...")
        
        from fmpy.gui.generated.MainWindow import Ui_MainWindow
        from fmpy.gui.MainWindow import MainWindow
        
        print("✓ FMPy GUI classes imported")
        
        # Try to find the simulation method
        # The GUI likely uses the same simulate_fmu function but with specific settings
        
    except Exception as e:
        print(f"✗ Method 2 failed: {e}")

def try_fmpy_gui_environment(fmu_path):
    """Try to replicate FMPy GUI environment exactly"""
    print("\nReplicating FMPy GUI environment...")
    print("=" * 50)
    
    # Set environment variables that FMPy GUI might set
    env_vars = {
        'FMPY_GUI_QT_BACKEND': 'PySide2',
        'QT_API': 'pyside2',
        'PYTHONPATH': os.getcwd(),
    }
    
    original_env = {}
    for key, value in env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
        print(f"Set {key} = {value}")
    
    # Add current directory to PATH (for DLLs)
    original_path = os.environ.get('PATH', '')
    os.environ['PATH'] = os.getcwd() + os.pathsep + original_path
    
    try:
        # Try simulation with GUI-like environment
        from fmpy import simulate_fmu
        
        print(f"\nAttempting simulation with GUI environment...")
        
        # Use the most basic settings first
        result = simulate_fmu(
            fmu_path,
            stop_time=1.0,
            validate=True,
            debug_logging=True,  # Enable debug logging to see what happens
            apply_default_start_values=True
        )
        
        print(f"✓ SUCCESS with GUI environment! Result shape: {result.shape}")
        return result
        
    except Exception as e:
        print(f"✗ Failed even with GUI environment: {e}")
        return None
    
    finally:
        # Restore environment
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value
        os.environ['PATH'] = original_path

def try_subprocess_approach(fmu_path):
    """Try running FMPy as a subprocess like the GUI might"""
    print("\nTrying subprocess approach...")
    print("=" * 50)
    
    try:
        # Create a simple script that just simulates the FMU
        script_content = f'''
import os
import sys

# Add current directory to PATH for DLLs
current_dir = r"{os.getcwd()}"
original_path = os.environ.get('PATH', '')
os.environ['PATH'] = current_dir + os.pathsep + original_path

# Set FMPy environment
os.environ['FMPY_GUI_QT_BACKEND'] = 'PySide2'

try:
    from fmpy import simulate_fmu
    
    result = simulate_fmu(
        r"{fmu_path}",
        stop_time=2.0,
        validate=True,
        apply_default_start_values=True,
        debug_logging=False
    )
    
    print(f"SUCCESS: Result shape {{result.shape}}")
    print(f"Columns: {{list(result.dtype.names)}}")
    
except Exception as e:
    print(f"ERROR: {{e}}")
    import traceback
    traceback.print_exc()
'''
        
        # Write script to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            script_path = f.name
        
        try:
            # Run the script as a subprocess
            print(f"Running simulation in subprocess...")
            result = subprocess.run([
                sys.executable, script_path
            ], capture_output=True, text=True, timeout=30)
            
            print("SUBPROCESS OUTPUT:")
            print(result.stdout)
            
            if result.stderr:
                print("SUBPROCESS ERRORS:")
                print(result.stderr)
            
            if result.returncode == 0 and "SUCCESS" in result.stdout:
                print("✓ Subprocess simulation succeeded!")
                return True
            else:
                print("✗ Subprocess simulation failed")
                return False
                
        finally:
            # Clean up temp script
            try:
                os.unlink(script_path)
            except:
                pass
                
    except Exception as e:
        print(f"✗ Subprocess approach failed: {e}")
        return False

def analyze_working_directory(fmu_path):
    """Check if working directory affects the simulation"""
    print("\nAnalyzing working directory effects...")
    print("=" * 50)
    
    # Try running from different directories
    fmu_dir = os.path.dirname(os.path.abspath(fmu_path))
    current_dir = os.getcwd()
    
    print(f"FMU directory: {fmu_dir}")
    print(f"Current directory: {current_dir}")
    
    if fmu_dir != current_dir:
        print(f"Trying to run from FMU directory...")
        
        try:
            # Change to FMU directory
            os.chdir(fmu_dir)
            
            # Add both directories to PATH
            os.environ['PATH'] = fmu_dir + os.pathsep + current_dir + os.pathsep + os.environ.get('PATH', '')
            
            from fmpy import simulate_fmu
            
            result = simulate_fmu(
                os.path.basename(fmu_path),  # Use relative path
                stop_time=1.0,
                validate=True,
                apply_default_start_values=True
            )
            
            print(f"✓ SUCCESS from FMU directory! Result shape: {result.shape}")
            return result
            
        except Exception as e:
            print(f"✗ Failed from FMU directory: {e}")
            return None
        finally:
            # Change back
            os.chdir(current_dir)
    else:
        print("Already in FMU directory")
        return None

def main():
    print("Exact FMPy GUI Replication Attempt")
    print("=" * 50)
    
    # Get FMU path
    if len(sys.argv) > 1:
        fmu_path = sys.argv[1]
    else:
        fmu_path = input("Enter path to FMU file: ").strip().strip('"')
    
    if not fmu_path.endswith('.fmu'):
        print("Error: Please provide a .fmu file")
        return
    
    if not os.path.exists(fmu_path):
        print(f"Error: FMU file not found: {fmu_path}")
        return
    
    print(f"Testing FMU: {fmu_path}")
    
    # Try different approaches
    approaches = [
        lambda: try_fmpy_gui_environment(fmu_path),
        lambda: analyze_working_directory(fmu_path),
        lambda: try_subprocess_approach(fmu_path),
        lambda: run_fmpy_gui_programmatically(fmu_path)
    ]
    
    for i, approach in enumerate(approaches, 1):
        print(f"\n{'='*60}")
        print(f"APPROACH {i}")
        print(f"{'='*60}")
        
        try:
            result = approach()
            if result is not None and result is not False:
                print(f"\n🎉 APPROACH {i} WORKED!")
                print("This approach successfully simulated the FMU.")
                break
        except Exception as e:
            print(f"Approach {i} crashed: {e}")
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    print("If none of these approaches worked, but FMPy GUI works,")
    print("the issue might be:")
    print("1. FMPy GUI uses a different Python process/environment")
    print("2. GUI has different DLL loading behavior")
    print("3. GUI uses different Qt/PySide environment")
    print("4. Timing differences in initialization")

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
