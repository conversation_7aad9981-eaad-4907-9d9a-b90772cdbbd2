import os
import sys

# Set environment variable before any imports
os.environ['FMPY_GUI_QT_BACKEND'] = 'PySide2'

# Force PySide2 to be used
try:
    import PySide2
    print("PySide2 is available")
except ImportError:
    print("PySide2 is not available")
    sys.exit(1)

# Now import FMPy GUI
try:
    from fmpy.gui import main
    print("FMPy GUI imported successfully")
except ImportError as e:
    print(f"Failed to import FMPy GUI: {e}")
    sys.exit(1)

def main_wrapper():
    print("Starting FMPy GUI with PySide2 backend...")
    main()

if __name__ == '__main__':
    main_wrapper()
