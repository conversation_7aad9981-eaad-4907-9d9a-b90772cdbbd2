#!/usr/bin/env python3
"""
Mimic exactly what FMPy GUI does for simulation
"""

import os
import sys

def setup_environment():
    """Setup environment like FMPy GUI"""
    current_dir = os.getcwd()
    original_path = os.environ.get('PATH', '')
    os.environ['PATH'] = current_dir + os.pathsep + original_path
    return original_path

def simulate_like_fmpy_gui(fmu_path):
    """Simulate FMU exactly like FMPy GUI does"""
    print(f"Simulating FMU like FMPy GUI: {fmu_path}")
    print("=" * 50)
    
    try:
        from fmpy import simulate_fmu, read_model_description
        
        # Step 1: Read model description (like GUI does)
        print("Reading model description...")
        model_desc = read_model_description(fmu_path)
        print(f"✓ Model: {model_desc.modelName}")
        print(f"✓ FMI Version: {model_desc.fmiVersion}")
        
        # Step 2: Use FMPy GUI default parameters
        print("\nUsing FMPy GUI default simulation parameters...")
        
        # These are the exact defaults that FMPy GUI uses
        result = simulate_fmu(
            filename=fmu_path,
            validate=True,                    # GUI validates by default
            start_time=0.0,                   # GUI default
            stop_time=2.0,                    # GUI default
            solver='CVode',                   # GUI default solver
            step_size=None,                   # Let solver decide
            output_interval=None,             # Let FMPy decide
            record_inputs=True,               # GUI records inputs
            record_outputs=True,              # GUI records outputs
            timeout=None,                     # No timeout
            debug_logging=False,              # No debug logging
            visible=False,                    # Not visible
            fmi_call_logger=None,            # No call logging
            step_finished=None,              # No step callback
            model_description=None,          # Let FMPy read it
            input=None,                      # No input file
            output=None,                     # No specific outputs
            start_values=None,               # No start values
            apply_default_start_values=True, # Apply defaults like GUI
            relative_tolerance=None,         # Use model default
            use_source_code=False           # Use binary
        )
        
        print(f"✓ SUCCESS! Simulation completed")
        print(f"  Result shape: {result.shape}")
        print(f"  Result columns: {list(result.dtype.names)}")
        
        # Show some results like GUI would
        if len(result) > 0:
            print(f"\nFirst few time points:")
            for i in range(min(5, len(result))):
                print(f"  t={result['time'][i]:.3f}")
        
        return result
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_manual_settings(fmu_path):
    """Compare GUI-like simulation with manual settings"""
    print(f"\n" + "=" * 50)
    print("COMPARING DIFFERENT SIMULATION APPROACHES")
    print("=" * 50)
    
    from fmpy import simulate_fmu
    
    approaches = [
        {
            'name': 'FMPy GUI Exact Defaults',
            'params': {
                'validate': True,
                'stop_time': 2.0,
                'solver': 'CVode',
                'apply_default_start_values': True,
                'record_inputs': True,
                'record_outputs': True
            }
        },
        {
            'name': 'Minimal Settings',
            'params': {
                'validate': False,
                'stop_time': 0.1,
                'step_size': 0.01,
                'debug_logging': False
            }
        },
        {
            'name': 'Conservative Settings',
            'params': {
                'validate': False,
                'stop_time': 1.0,
                'solver': 'Euler',
                'step_size': 0.1,
                'relative_tolerance': 1e-3
            }
        }
    ]
    
    for approach in approaches:
        print(f"\nTrying: {approach['name']}")
        print("-" * 30)
        
        try:
            result = simulate_fmu(fmu_path, **approach['params'])
            print(f"✓ SUCCESS! Shape: {result.shape}")
        except Exception as e:
            print(f"✗ FAILED: {str(e)[:100]}...")

def main():
    print("FMPy GUI Simulation Mimic")
    print("=" * 50)
    
    # Setup environment
    original_path = setup_environment()
    
    try:
        # Get FMU path
        if len(sys.argv) > 1:
            fmu_path = sys.argv[1]
        else:
            fmu_path = input("Enter path to FMU file: ").strip().strip('"')
        
        if not fmu_path.endswith('.fmu'):
            print("Error: Please provide a .fmu file")
            return
        
        if not os.path.exists(fmu_path):
            print(f"Error: FMU file not found: {fmu_path}")
            return
        
        # Try to simulate exactly like FMPy GUI
        result = simulate_like_fmpy_gui(fmu_path)
        
        if result is not None:
            print(f"\n🎉 SUCCESS! The FMU works with FMPy GUI settings!")
            print("This means the issue in your UI is likely due to different simulation parameters.")
            print("\nRecommendation: Update your UI to use these exact settings.")
        else:
            print(f"\n😞 Even FMPy GUI settings failed.")
            print("This suggests a deeper compatibility issue.")
            
            # Try different approaches
            compare_with_manual_settings(fmu_path)
    
    finally:
        # Restore PATH
        os.environ['PATH'] = original_path

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
