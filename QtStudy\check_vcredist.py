#!/usr/bin/env python3
"""
Check for installed Visual C++ Redistributables
"""

import winreg
import platform

def check_vcredist():
    """Check for Visual C++ Redistributables in registry"""
    print("Checking for Visual C++ Redistributables...")
    print(f"Python architecture: {platform.architecture()[0]}")
    print("-" * 50)
    
    # Registry paths to check
    registry_paths = [
        # Visual Studio 2015-2022 (v14.x)
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64", "VC++ 2015-2022 x64"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86", "VC++ 2015-2022 x86"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x64", "VC++ 2015-2022 x64 (WOW64)"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x86", "VC++ 2015-2022 x86 (WOW64)"),
        
        # Visual Studio 2013 (v12.0)
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\DevDiv\vc\Servicing\12.0\RuntimeMinimum", "VC++ 2013 x64"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\Microsoft\DevDiv\vc\Servicing\12.0\RuntimeMinimum", "VC++ 2013 x86"),
        
        # Visual Studio 2012 (v11.0)
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\DevDiv\vc\Servicing\11.0\RuntimeMinimum", "VC++ 2012 x64"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\Microsoft\DevDiv\vc\Servicing\11.0\RuntimeMinimum", "VC++ 2012 x86"),
        
        # Visual Studio 2010 (v10.0)
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\DevDiv\vc\Servicing\10.0\RuntimeMinimum", "VC++ 2010 x64"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\Microsoft\DevDiv\vc\Servicing\10.0\RuntimeMinimum", "VC++ 2010 x86"),
    ]
    
    found_any = False
    
    for hkey, path, description in registry_paths:
        try:
            key = winreg.OpenKey(hkey, path)
            try:
                # Try to get version info
                version = winreg.QueryValueEx(key, "Version")[0]
                print(f"✓ {description} - Version: {version}")
                found_any = True
            except FileNotFoundError:
                # Key exists but no version info
                print(f"✓ {description} - Installed (no version info)")
                found_any = True
            winreg.CloseKey(key)
        except FileNotFoundError:
            # Registry key doesn't exist
            continue
        except Exception as e:
            print(f"? {description} - Error checking: {e}")
    
    if not found_any:
        print("✗ No Visual C++ Redistributables found!")
        print("\nRECOMMENDATION:")
        print("Download and install Microsoft Visual C++ Redistributable packages:")
        print("- VC++ 2015-2022 Redistributable (x86) - for 32-bit applications")
        print("- VC++ 2015-2022 Redistributable (x64) - for 64-bit applications")
        print("- Available from: https://docs.microsoft.com/en-us/cpp/windows/latest-supported-vc-redist")
    else:
        print(f"\n✓ Found Visual C++ Redistributables")
        print("\nIf you're still getting DLL errors, the issue might be:")
        print("1. Missing specific runtime libraries")
        print("2. DLL dependencies not in PATH")
        print("3. Corrupted installation")

def check_common_dlls():
    """Check for common runtime DLLs"""
    print("\n" + "="*50)
    print("Checking for common runtime DLLs...")
    print("="*50)
    
    import ctypes
    
    common_dlls = [
        "msvcr120.dll",  # VC++ 2013
        "msvcp120.dll",  # VC++ 2013
        "msvcr140.dll",  # VC++ 2015-2022
        "msvcp140.dll",  # VC++ 2015-2022
        "vcruntime140.dll",  # VC++ 2015-2022
        "api-ms-win-crt-runtime-l1-1-0.dll",  # Universal CRT
        "kernel32.dll",  # Windows system
        "user32.dll",    # Windows system
        "advapi32.dll",  # Windows system
    ]
    
    for dll in common_dlls:
        try:
            ctypes.WinDLL(dll)
            print(f"✓ {dll}")
        except OSError:
            print(f"✗ {dll} - NOT FOUND")

if __name__ == "__main__":
    try:
        check_vcredist()
        check_common_dlls()
    except Exception as e:
        print(f"Error: {e}")
    
    print("\nPress Enter to exit...")
    input()
