#!/usr/bin/env python3
"""
Try different approaches to simulate the FMU
"""

import os
import sys

def setup_environment():
    """Setup environment for FMU simulation"""
    current_dir = os.getcwd()
    original_path = os.environ.get('PATH', '')
    os.environ['PATH'] = current_dir + os.pathsep + original_path
    return original_path

def approach_1_basic(fmu_path):
    """Approach 1: Most basic simulation possible"""
    print("APPROACH 1: Ultra-minimal simulation")
    print("-" * 40)
    
    try:
        from fmpy import simulate_fmu
        
        result = simulate_fmu(
            fmu_path,
            stop_time=0.001,  # 1 millisecond
            step_size=0.0001,  # 0.1 millisecond steps
            validate=False,
            debug_logging=False
        )
        print(f"✓ SUCCESS! Result shape: {result.shape}")
        return True
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return False

def approach_2_with_solver(fmu_path):
    """Approach 2: Try with different solver"""
    print("\nAPPROACH 2: Different solver settings")
    print("-" * 40)
    
    try:
        from fmpy import simulate_fmu
        
        result = simulate_fmu(
            fmu_path,
            stop_time=0.01,
            solver='Euler',  # Simple Euler solver
            step_size=0.001,
            validate=False
        )
        print(f"✓ SUCCESS! Result shape: {result.shape}")
        return True
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return False

def approach_3_manual_fmi(fmu_path):
    """Approach 3: Manual FMI calls with error checking"""
    print("\nAPPROACH 3: Manual FMI interface")
    print("-" * 40)
    
    try:
        from fmpy import read_model_description, extract
        from fmpy.fmi2 import FMU2Slave
        import tempfile
        import shutil
        
        # Read model
        model_desc = read_model_description(fmu_path)
        print(f"✓ Model description read: {model_desc.modelName}")
        
        # Extract
        temp_dir = tempfile.mkdtemp()
        extract_dir = extract(fmu_path, temp_dir)
        print(f"✓ FMU extracted")
        
        try:
            # Create FMU instance with minimal settings
            fmu = FMU2Slave(
                guid=model_desc.guid,
                unzipDirectory=extract_dir,
                modelIdentifier=model_desc.coSimulation.modelIdentifier,
                instanceName="minimal_test"
            )
            print(f"✓ FMU2Slave created")
            
            # Instantiate
            fmu.instantiate()
            print(f"✓ FMU instantiated")
            
            # Setup experiment with minimal time
            fmu.setupExperiment(startTime=0.0, stopTime=0.001)
            print(f"✓ Experiment setup")
            
            # Enter initialization
            fmu.enterInitializationMode()
            print(f"✓ Entered initialization mode")
            
            # Exit initialization
            fmu.exitInitializationMode()
            print(f"✓ Exited initialization mode")
            
            # Do one tiny step
            fmu.doStep(currentCommunicationPoint=0.0, communicationStepSize=0.0001)
            print(f"✓ First step completed")
            
            # Terminate
            fmu.terminate()
            fmu.freeInstance()
            print(f"✓ FMU terminated and freed")
            
            return True
            
        finally:
            shutil.rmtree(temp_dir)
            
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return False

def approach_4_input_initialization(fmu_path):
    """Approach 4: Comprehensive input initialization"""
    print("\nAPPROACH 4: Comprehensive input initialization")
    print("-" * 40)
    
    try:
        from fmpy import simulate_fmu, read_model_description
        
        # Read model to get variables
        model_desc = read_model_description(fmu_path)
        
        # Create comprehensive start values
        start_values = {}
        
        for var in model_desc.modelVariables:
            if var.causality in ['input', 'parameter']:
                if var.start is not None:
                    start_values[var.name] = var.start
                elif var.type == 'Real':
                    # Use sensible defaults for BMS variables
                    name_lower = var.name.lower()
                    if any(keyword in name_lower for keyword in ['volt', 'voltage']):
                        start_values[var.name] = 12.0
                    elif any(keyword in name_lower for keyword in ['current', 'amp']):
                        start_values[var.name] = 1.0
                    elif any(keyword in name_lower for keyword in ['temp', 'temperature']):
                        start_values[var.name] = 25.0
                    elif any(keyword in name_lower for keyword in ['soc', 'charge']):
                        start_values[var.name] = 0.5
                    elif any(keyword in name_lower for keyword in ['power']):
                        start_values[var.name] = 10.0
                    elif any(keyword in name_lower for keyword in ['resistance', 'ohm']):
                        start_values[var.name] = 0.1
                    else:
                        start_values[var.name] = 1.0
                elif var.type == 'Integer':
                    start_values[var.name] = 1
                elif var.type == 'Boolean':
                    start_values[var.name] = True
        
        print(f"Initializing {len(start_values)} variables:")
        for name, value in list(start_values.items())[:5]:  # Show first 5
            print(f"  {name} = {value}")
        if len(start_values) > 5:
            print(f"  ... and {len(start_values) - 5} more")
        
        result = simulate_fmu(
            fmu_path,
            start_values=start_values,
            stop_time=0.01,
            step_size=0.001,
            validate=False,
            debug_logging=False
        )
        print(f"✓ SUCCESS! Result shape: {result.shape}")
        return True
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return False

def approach_5_different_fmpy_version(fmu_path):
    """Approach 5: Check FMPy version and suggest alternatives"""
    print("\nAPPROACH 5: Version compatibility check")
    print("-" * 40)
    
    try:
        import fmpy
        print(f"Current FMPy version: {fmpy.__version__}")
        
        # Try with different settings that work with older FMUs
        from fmpy import simulate_fmu
        
        result = simulate_fmu(
            fmu_path,
            stop_time=0.01,
            step_size=0.001,
            validate=False,
            debug_logging=False,
            fmi_call_logger=None,  # Disable logging
            record_inputs=False,   # Don't record inputs
            record_outputs=True    # Only record outputs
        )
        print(f"✓ SUCCESS with compatibility settings! Result shape: {result.shape}")
        return True
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        print("Consider trying:")
        print("  - pip install fmpy==0.3.20  # Try older version")
        print("  - Different FMU simulation tool")
        return False

def main():
    print("FMU Alternative Approaches Test")
    print("=" * 50)
    
    # Setup environment
    original_path = setup_environment()
    
    try:
        # Get FMU path
        if len(sys.argv) > 1:
            fmu_path = sys.argv[1]
        else:
            fmu_path = input("Enter path to FMU file: ").strip().strip('"')
        
        if not fmu_path.endswith('.fmu'):
            print("Error: Please provide a .fmu file")
            return
        
        print(f"Testing FMU: {fmu_path}")
        print("=" * 50)
        
        # Try different approaches
        approaches = [
            approach_1_basic,
            approach_2_with_solver,
            approach_3_manual_fmi,
            approach_4_input_initialization,
            approach_5_different_fmpy_version
        ]
        
        success_count = 0
        for approach in approaches:
            if approach(fmu_path):
                success_count += 1
                print(f"\n🎉 FOUND A WORKING APPROACH!")
                break  # Stop at first success
        
        if success_count == 0:
            print(f"\n😞 ALL APPROACHES FAILED")
            print("\nThis suggests a fundamental issue with the FMU:")
            print("1. FMU may be corrupted or incompatible")
            print("2. Missing critical dependencies")
            print("3. FMU requires specific simulation environment")
            print("4. Memory/pointer issues in FMU code")
            print("\nRecommendation: Contact the FMU developer")
        
    finally:
        # Restore PATH
        os.environ['PATH'] = original_path

if __name__ == "__main__":
    main()
    print("\nPress Enter to exit...")
    input()
